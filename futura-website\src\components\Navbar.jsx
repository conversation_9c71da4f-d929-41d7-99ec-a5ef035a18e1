import { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false)
  const location = useLocation()

  const isActive = (path) => location.pathname === path

  return (
    <nav className="bg-white/90 backdrop-blur-md shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link to="/" className="flex-shrink-0 flex items-center">
              <span className="text-2xl font-bold text-futura-primary font-futura">
                FUTURA
              </span>
              <span className="ml-2 text-sm text-futura-secondary">
                Materials
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              to="/"
              className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                isActive('/') 
                  ? 'text-futura-primary bg-futura-primary/10' 
                  : 'text-futura-secondary hover:text-futura-primary hover:bg-futura-primary/5'
              }`}
            >
              Home
            </Link>
            <Link
              to="/about"
              className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                isActive('/about') 
                  ? 'text-futura-primary bg-futura-primary/10' 
                  : 'text-futura-secondary hover:text-futura-primary hover:bg-futura-primary/5'
              }`}
            >
              About
            </Link>
            <button className="bg-futura-primary text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-futura-primary/90 transition-colors">
              Contact Us
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-futura-secondary hover:text-futura-primary focus:outline-none focus:text-futura-primary"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white/95 backdrop-blur-md rounded-lg mt-2 shadow-lg">
              <Link
                to="/"
                className={`block px-3 py-2 rounded-md text-base font-medium ${
                  isActive('/') 
                    ? 'text-futura-primary bg-futura-primary/10' 
                    : 'text-futura-secondary hover:text-futura-primary hover:bg-futura-primary/5'
                }`}
                onClick={() => setIsOpen(false)}
              >
                Home
              </Link>
              <Link
                to="/about"
                className={`block px-3 py-2 rounded-md text-base font-medium ${
                  isActive('/about') 
                    ? 'text-futura-primary bg-futura-primary/10' 
                    : 'text-futura-secondary hover:text-futura-primary hover:bg-futura-primary/5'
                }`}
                onClick={() => setIsOpen(false)}
              >
                About
              </Link>
              <button className="w-full text-left bg-futura-primary text-white px-3 py-2 rounded-md text-base font-medium hover:bg-futura-primary/90 transition-colors">
                Contact Us
              </button>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}

export default Navbar
