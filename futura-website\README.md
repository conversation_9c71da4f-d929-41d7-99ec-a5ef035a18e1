# Futura Materials Website

A modern, responsive website showcasing Futura materials and their innovative applications. Built with React, Vite, and Tailwind CSS.

## Features

- **Responsive Design**: Optimized for all device sizes
- **Modern UI**: Clean, professional design with smooth animations
- **Material Showcase**: Interactive cards displaying material properties and applications
- **About Page**: Company information, team, timeline, and values
- **Modal System**: Detailed material information in popup modals
- **Navigation**: Smooth routing between pages
- **Accessibility**: Built with accessibility best practices

## Tech Stack

- **Frontend**: React 18 with Vite
- **Styling**: Tailwind CSS
- **Routing**: React Router DOM
- **Icons**: Heroicons (via SVG)
- **Build Tool**: Vite
- **Linting**: ESLint

## Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd futura-website
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

4. Open your browser and navigate to `http://localhost:3000`

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Project Structure

```
futura-website/
├── public/
│   ├── vite.svg
│   └── images/ (add your material photos here)
├── src/
│   ├── components/
│   │   ├── Navbar.jsx
│   │   ├── Footer.jsx
│   │   └── Card.jsx
│   ├── pages/
│   │   ├── Home.jsx
│   │   └── About.jsx
│   ├── App.jsx
│   ├── main.jsx
│   └── index.css
├── tailwind.config.js
├── vite.config.js
└── package.json
```

## Customization

### Adding Your Images

1. Add your material photos to the `public/images/` directory
2. Update the image paths in the components (currently using placeholder URLs)
3. Replace `/api/placeholder/` URLs with your actual image paths

### Updating Content

- **Materials**: Edit the `materials` array in `src/pages/Home.jsx`
- **Team Members**: Update the `teamMembers` array in `src/pages/About.jsx`
- **Company Info**: Modify the content in both Home and About pages
- **Colors**: Adjust the color scheme in `tailwind.config.js`

### Styling

The website uses a custom color palette defined in `tailwind.config.js`:
- Primary: Blue (#2563eb)
- Secondary: Slate (#64748b)
- Accent: Amber (#f59e0b)
- Dark: Slate (#1e293b)
- Light: Slate (#f8fafc)

## Deployment

### Build for Production

```bash
npm run build
```

The built files will be in the `dist/` directory, ready for deployment to any static hosting service.

### Deployment Options

- **Vercel**: Connect your GitHub repository for automatic deployments
- **Netlify**: Drag and drop the `dist/` folder or connect via Git
- **GitHub Pages**: Use GitHub Actions to deploy the built files
- **Traditional Hosting**: Upload the `dist/` folder contents to your web server

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
