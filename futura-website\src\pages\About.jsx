import Card from '../components/Card'

const About = () => {
  const teamMembers = [
    {
      name: "Dr. <PERSON>",
      role: "Chief Technology Officer",
      bio: "Leading materials scientist with 15+ years in advanced composites research.",
      image: "/api/placeholder/300/300"
    },
    {
      name: "<PERSON>",
      role: "Head of Innovation",
      bio: "Expert in nano-materials and sustainable material development.",
      image: "/api/placeholder/300/300"
    },
    {
      name: "Dr. <PERSON>",
      role: "Research Director",
      bio: "Pioneering work in smart materials and bio-compatible polymers.",
      image: "/api/placeholder/300/300"
    }
  ]

  const milestones = [
    { year: "2018", event: "Founded Futura Materials with breakthrough composite technology" },
    { year: "2019", event: "First commercial application in aerospace industry" },
    { year: "2020", event: "Launched sustainable bio-polymer line" },
    { year: "2021", event: "Opened state-of-the-art research facility" },
    { year: "2022", event: "Introduced smart materials division" },
    { year: "2023", event: "Achieved carbon-neutral manufacturing" },
    { year: "2024", event: "Expanded to global markets with 50+ partners" }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-futura-dark to-futura-primary text-white py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-5xl font-bold mb-6 font-futura">
                About Futura Materials
              </h1>
              <p className="text-xl mb-8 opacity-90">
                We are pioneers in advanced material science, dedicated to creating innovative solutions 
                that push the boundaries of what's possible in engineering and design.
              </p>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <div className="text-3xl font-bold text-futura-accent">50+</div>
                  <div className="text-sm opacity-80">Global Partners</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-futura-accent">200+</div>
                  <div className="text-sm opacity-80">Patents Filed</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-futura-accent">15+</div>
                  <div className="text-sm opacity-80">Years Experience</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-futura-accent">100%</div>
                  <div className="text-sm opacity-80">Carbon Neutral</div>
                </div>
              </div>
            </div>
            <div className="relative">
              <img 
                src="/api/placeholder/600/400" 
                alt="Futura Materials Laboratory"
                className="rounded-lg shadow-2xl"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12">
            <Card className="h-full">
              <div className="text-center">
                <div className="w-16 h-16 bg-futura-primary rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-futura-dark mb-4">Our Mission</h3>
                <p className="text-futura-secondary leading-relaxed">
                  To revolutionize industries through innovative material solutions that enhance performance, 
                  sustainability, and human potential. We strive to create materials that not only meet today's 
                  challenges but anticipate tomorrow's needs.
                </p>
              </div>
            </Card>

            <Card className="h-full">
              <div className="text-center">
                <div className="w-16 h-16 bg-futura-accent rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-futura-dark mb-4">Our Vision</h3>
                <p className="text-futura-secondary leading-relaxed">
                  To be the global leader in advanced materials, enabling a sustainable future where technology 
                  and nature work in harmony. We envision a world where our materials contribute to cleaner 
                  energy, smarter cities, and healthier lives.
                </p>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Timeline */}
      <section className="bg-gray-50 py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-futura-dark mb-4">Our Journey</h2>
            <p className="text-xl text-futura-secondary">Key milestones in our mission to advance material science</p>
          </div>

          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-futura-primary"></div>
            
            <div className="space-y-8">
              {milestones.map((milestone, index) => (
                <div key={index} className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <div className="bg-white p-6 rounded-lg shadow-lg">
                      <div className="text-2xl font-bold text-futura-primary mb-2">{milestone.year}</div>
                      <p className="text-futura-secondary">{milestone.event}</p>
                    </div>
                  </div>
                  
                  <div className="relative z-10">
                    <div className="w-4 h-4 bg-futura-accent rounded-full border-4 border-white shadow-lg"></div>
                  </div>
                  
                  <div className="w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-futura-dark mb-4">Meet Our Team</h2>
            <p className="text-xl text-futura-secondary">The brilliant minds behind Futura Materials</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <Card key={index} className="text-center">
                <img 
                  src={member.image} 
                  alt={member.name}
                  className="w-32 h-32 rounded-full mx-auto mb-4 object-cover"
                />
                <h3 className="text-xl font-semibold text-futura-dark mb-2">{member.name}</h3>
                <p className="text-futura-primary font-medium mb-3">{member.role}</p>
                <p className="text-futura-secondary text-sm">{member.bio}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="bg-futura-dark text-white py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4">Our Values</h2>
            <p className="text-xl opacity-90">The principles that guide everything we do</p>
          </div>

          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-futura-accent rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold mb-2">Innovation</h3>
              <p className="text-sm opacity-80">Constantly pushing boundaries and exploring new possibilities</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-futura-accent rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold mb-2">Sustainability</h3>
              <p className="text-sm opacity-80">Committed to environmental responsibility in all our processes</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-futura-accent rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold mb-2">Collaboration</h3>
              <p className="text-sm opacity-80">Working together with partners to achieve shared goals</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-futura-accent rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold mb-2">Excellence</h3>
              <p className="text-sm opacity-80">Maintaining the highest standards in everything we deliver</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default About
