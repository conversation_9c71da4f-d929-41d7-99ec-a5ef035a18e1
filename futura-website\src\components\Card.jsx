const Card = ({ 
  title, 
  description, 
  image, 
  imageAlt, 
  className = "", 
  children,
  onClick,
  hover = true 
}) => {
  return (
    <div 
      className={`
        bg-white rounded-xl shadow-lg overflow-hidden 
        ${hover ? 'hover:shadow-xl hover:scale-105' : ''} 
        transition-all duration-300 cursor-pointer
        ${className}
      `}
      onClick={onClick}
    >
      {image && (
        <div className="aspect-w-16 aspect-h-9 overflow-hidden">
          <img 
            src={image} 
            alt={imageAlt || title} 
            className="w-full h-48 object-cover"
          />
        </div>
      )}
      
      <div className="p-6">
        {title && (
          <h3 className="text-xl font-semibold text-futura-dark mb-3">
            {title}
          </h3>
        )}
        
        {description && (
          <p className="text-futura-secondary mb-4 leading-relaxed">
            {description}
          </p>
        )}
        
        {children}
      </div>
    </div>
  )
}

export default Card
