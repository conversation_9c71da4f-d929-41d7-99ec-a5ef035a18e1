import { useState } from 'react'
import Card from '../components/Card'

const Home = () => {
  const [selectedMaterial, setSelectedMaterial] = useState(null)

  const materials = [
    {
      id: 1,
      title: "Futura Composite X1",
      description: "Ultra-lightweight composite material with exceptional strength-to-weight ratio. Perfect for aerospace and automotive applications.",
      properties: ["Weight: 40% lighter than aluminum", "Tensile Strength: 850 MPa", "Temperature Resistance: -40°C to 200°C"],
      applications: ["Aerospace components", "Automotive parts", "Sports equipment"],
      image: "/api/placeholder/400/300"
    },
    {
      id: 2,
      title: "Futura Nano-Ceramic",
      description: "Advanced nano-ceramic material offering superior thermal and electrical insulation properties.",
      properties: ["Thermal Conductivity: 0.02 W/mK", "Dielectric Strength: 40 kV/mm", "Hardness: 9.5 Mohs"],
      applications: ["Electronics", "Thermal barriers", "Cutting tools"],
      image: "/api/placeholder/400/300"
    },
    {
      id: 3,
      title: "Futura Bio-Polymer",
      description: "Sustainable bio-based polymer with excellent biodegradability and mechanical properties.",
      properties: ["100% biodegradable", "Tensile Strength: 45 MPa", "Elongation: 300%"],
      applications: ["Packaging", "Medical devices", "Consumer goods"],
      image: "/api/placeholder/400/300"
    },
    {
      id: 4,
      title: "Futura Smart Alloy",
      description: "Shape-memory alloy that responds to temperature changes, enabling smart material applications.",
      properties: ["Shape Recovery: 95%", "Activation Temperature: 60°C", "Fatigue Life: >10M cycles"],
      applications: ["Actuators", "Medical implants", "Smart textiles"],
      image: "/api/placeholder/400/300"
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-futura-primary to-futura-accent text-white py-20 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-6 font-futura">
            The Future of Materials
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
            Discover revolutionary materials that are reshaping industries and enabling tomorrow's innovations
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-white text-futura-primary px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Explore Materials
            </button>
            <button className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-futura-primary transition-colors">
              Download Catalog
            </button>
          </div>
        </div>
        
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-4 -right-4 w-72 h-72 bg-white/10 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-8 -left-8 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>
        </div>
      </section>

      {/* Materials Showcase */}
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-futura-dark mb-4">
              Our Material Portfolio
            </h2>
            <p className="text-xl text-futura-secondary max-w-3xl mx-auto">
              Explore our cutting-edge materials designed for the most demanding applications
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8">
            {materials.map((material) => (
              <Card
                key={material.id}
                title={material.title}
                description={material.description}
                image={material.image}
                imageAlt={material.title}
                onClick={() => setSelectedMaterial(material)}
                className="h-full"
              >
                <div className="space-y-2">
                  <h4 className="font-semibold text-futura-dark">Key Properties:</h4>
                  <ul className="text-sm text-futura-secondary space-y-1">
                    {material.properties.slice(0, 2).map((prop, index) => (
                      <li key={index} className="flex items-center">
                        <span className="w-2 h-2 bg-futura-accent rounded-full mr-2"></span>
                        {prop}
                      </li>
                    ))}
                  </ul>
                  <button className="mt-4 text-futura-primary font-semibold hover:text-futura-accent transition-colors">
                    Learn More →
                  </button>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="bg-gray-50 py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-futura-dark mb-4">
              Why Choose Futura Materials?
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-futura-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Innovation</h3>
              <p className="text-futura-secondary">Cutting-edge research and development driving material science forward</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-futura-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Quality</h3>
              <p className="text-futura-secondary">Rigorous testing and quality control ensuring superior performance</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-futura-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Sustainability</h3>
              <p className="text-futura-secondary">Environmentally conscious materials for a sustainable future</p>
            </div>
          </div>
        </div>
      </section>

      {/* Material Detail Modal */}
      {selectedMaterial && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-2xl font-bold text-futura-dark">{selectedMaterial.title}</h3>
                <button 
                  onClick={() => setSelectedMaterial(null)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <img 
                src={selectedMaterial.image} 
                alt={selectedMaterial.title}
                className="w-full h-64 object-cover rounded-lg mb-4"
              />
              
              <p className="text-futura-secondary mb-6">{selectedMaterial.description}</p>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-futura-dark mb-3">Properties</h4>
                  <ul className="space-y-2">
                    {selectedMaterial.properties.map((prop, index) => (
                      <li key={index} className="flex items-center text-sm">
                        <span className="w-2 h-2 bg-futura-accent rounded-full mr-2"></span>
                        {prop}
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold text-futura-dark mb-3">Applications</h4>
                  <ul className="space-y-2">
                    {selectedMaterial.applications.map((app, index) => (
                      <li key={index} className="flex items-center text-sm">
                        <span className="w-2 h-2 bg-futura-primary rounded-full mr-2"></span>
                        {app}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
              
              <div className="mt-6 flex gap-4">
                <button className="bg-futura-primary text-white px-6 py-2 rounded-lg hover:bg-futura-primary/90 transition-colors">
                  Request Sample
                </button>
                <button className="border border-futura-primary text-futura-primary px-6 py-2 rounded-lg hover:bg-futura-primary/5 transition-colors">
                  Download Datasheet
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Home
